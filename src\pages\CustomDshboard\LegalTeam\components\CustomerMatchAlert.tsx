import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, User, Phone, Mail, CreditCard } from 'lucide-react';
import { CustomerMatch } from '../utils/customerCrossCheck';

interface CustomerMatchAlertProps {
  matches: CustomerMatch[];
  customerType: 'individual' | 'partner' | 'member' | 'director';
  customerName: string;
}

const CustomerMatchAlert: React.FC<CustomerMatchAlertProps> = ({
  matches,
  customerType,
  customerName
}) => {
  if (!matches || matches.length === 0) {
    return null;
  }

  const getMatchIcon = (field: string) => {
    switch (field) {
      case 'name':
        return <User className="w-3 h-3" />;
      case 'phone':
        return <Phone className="w-3 h-3" />;
      case 'email':
        return <Mail className="w-3 h-3" />;
      case 'national_id':
        return <CreditCard className="w-3 h-3" />;
      default:
        return <CheckCircle className="w-3 h-3" />;
    }
  };

  const getFieldLabel = (field: string) => {
    switch (field) {
      case 'name':
        return 'Name';
      case 'phone':
        return 'Phone';
      case 'email':
        return 'Email';
      case 'national_id':
        return 'National ID';
      default:
        return field;
    }
  };

  const getCustomerTypeLabel = (type: string) => {
    switch (type) {
      case 'individual':
        return 'Individual';
      case 'partner':
        return 'Partner';
      case 'member':
        return 'Group Member';
      case 'director':
        return 'Company Director';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-2">
      {matches.map((match, index) => (
        <Alert key={index} className="border-orange-200 bg-orange-50 dark:bg-orange-900/20">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-sm">
            <div className="space-y-2">
              <div className="font-medium text-orange-800 dark:text-orange-200">
                ⚠️ Customer Record Found in Database
              </div>
              
              <div className="text-orange-700 dark:text-orange-300">
                <strong>{getCustomerTypeLabel(customerType)}</strong> "{customerName}" 
                already exists as <strong>{match.customer.customer_name}</strong> 
                (Customer No: <strong>{match.customer.customer_no}</strong>)
              </div>

              <div className="flex flex-wrap gap-1 mt-2">
                <span className="text-xs text-orange-600 dark:text-orange-400 mr-2">
                  Matched fields:
                </span>
                {match.matchedFields.map((field, fieldIndex) => (
                  <Badge 
                    key={fieldIndex} 
                    variant="outline" 
                    className="text-xs border-orange-300 text-orange-700 dark:text-orange-300 flex items-center gap-1"
                  >
                    {getMatchIcon(field)}
                    {getFieldLabel(field)}
                  </Badge>
                ))}
              </div>

              <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <strong>Phone:</strong> {match.customer.phone || 'N/A'}
                  </div>
                  <div>
                    <strong>Email:</strong> {match.customer.primary_email || 'N/A'}
                  </div>
                  <div>
                    <strong>National ID:</strong> {match.customer.national_id || 'N/A'}
                  </div>
                  <div>
                    <strong>Registration:</strong> {match.customer.date_of_registration || 'N/A'}
                  </div>
                </div>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </div>
  );
};

export default CustomerMatchAlert;
