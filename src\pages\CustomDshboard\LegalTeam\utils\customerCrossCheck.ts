// Utility functions for cross-checking offer letter customers with existing database records

export interface ExistingCustomer {
  customer_no: string;
  customer_name: string;
  national_id: string;
  passport_no: string;
  kra_pin: string;
  dob: string | null;
  gender: string;
  marital_status: string;
  phone: string;
  alternative_phone: string;
  primary_email: string;
  alternative_email: string;
  address: string;
  customer_type: string;
  country_of_residence: string;
  date_of_registration: string;
  otp: string | null;
  otp_generated_at: string | null;
  lead_source: string | null;
  marketer: string;
}

export interface CustomerMatch {
  customer: ExistingCustomer;
  matchedFields: string[];
  matchType: 'exact' | 'partial';
}

// Normalize phone numbers for comparison
const normalizePhone = (phone: string): string => {
  if (!phone) return '';
  // Remove all non-digit characters and normalize format
  const cleaned = phone.replace(/\D/g, '');
  // Handle different formats (254, +254, 0)
  if (cleaned.startsWith('254')) return cleaned;
  if (cleaned.startsWith('0')) return '254' + cleaned.substring(1);
  return cleaned;
};

// Normalize email for comparison
const normalizeEmail = (email: string): string => {
  return email ? email.toLowerCase().trim() : '';
};

// Normalize name for comparison
const normalizeName = (name: string): string => {
  return name ? name.toLowerCase().trim().replace(/\s+/g, ' ') : '';
};

// Check if individual customer matches existing records
export const checkIndividualCustomerMatch = (
  individual: {
    first_name?: string;
    last_name?: string;
    phone?: string;
    email?: string;
    national_id?: string;
  },
  existingCustomers: ExistingCustomer[]
): CustomerMatch[] => {
  const matches: CustomerMatch[] = [];
  
  const individualFullName = normalizeName(`${individual.first_name || ''} ${individual.last_name || ''}`.trim());
  const individualPhone = normalizePhone(individual.phone || '');
  const individualEmail = normalizeEmail(individual.email || '');
  const individualNationalId = individual.national_id?.trim() || '';

  existingCustomers.forEach(customer => {
    const matchedFields: string[] = [];
    let isMatch = false;

    // Check name match
    const customerName = normalizeName(customer.customer_name);
    if (individualFullName && customerName && customerName === individualFullName) {
      matchedFields.push('name');
      isMatch = true;
    }

    // Check phone match
    const customerPhone = normalizePhone(customer.phone);
    if (individualPhone && customerPhone && customerPhone === individualPhone) {
      matchedFields.push('phone');
      isMatch = true;
    }

    // Check email match
    const customerEmail = normalizeEmail(customer.primary_email);
    if (individualEmail && customerEmail && customerEmail === individualEmail) {
      matchedFields.push('email');
      isMatch = true;
    }

    // Check national ID match
    if (individualNationalId && customer.national_id && 
        customer.national_id.trim() === individualNationalId) {
      matchedFields.push('national_id');
      isMatch = true;
    }

    if (isMatch) {
      matches.push({
        customer,
        matchedFields,
        matchType: matchedFields.length >= 2 ? 'exact' : 'partial'
      });
    }
  });

  return matches;
};

// Check if partner customer matches existing records
export const checkPartnerCustomerMatch = (
  partner: {
    first_name?: string;
    last_name?: string;
    phone?: string;
    email?: string;
    national_id?: string;
  },
  existingCustomers: ExistingCustomer[]
): CustomerMatch[] => {
  // Partners use the same logic as individuals
  return checkIndividualCustomerMatch(partner, existingCustomers);
};

// Check if group member matches existing records
export const checkGroupMemberMatch = (
  member: {
    first_name?: string;
    last_name?: string;
    phone?: string;
    email?: string;
    national_id?: string;
  },
  existingCustomers: ExistingCustomer[]
): CustomerMatch[] => {
  // Group members use the same logic as individuals
  return checkIndividualCustomerMatch(member, existingCustomers);
};

// Check if company director matches existing records
export const checkDirectorMatch = (
  director: {
    first_name?: string;
    last_name?: string;
    phone?: string;
    email?: string;
    national_id?: string;
  },
  existingCustomers: ExistingCustomer[]
): CustomerMatch[] => {
  // Directors use the same logic as individuals
  return checkIndividualCustomerMatch(director, existingCustomers);
};

// Get all individual customers from offer letter data for cross-checking
export const getAllIndividualsFromOfferLetter = (detailsData: any): any[] => {
  const individuals: any[] = [];

  // Add individuals
  if (detailsData.individuals) {
    individuals.push(...detailsData.individuals);
  }

  // Add partners
  if (detailsData.partners) {
    individuals.push(...detailsData.partners);
  }

  // Add group members
  if (detailsData.groups) {
    detailsData.groups.forEach((group: any) => {
      if (group.members) {
        individuals.push(...group.members);
      }
    });
  }

  // Add company directors
  if (detailsData.companies) {
    detailsData.companies.forEach((company: any) => {
      if (company.directors) {
        individuals.push(...company.directors);
      }
    });
  }

  return individuals;
};

// Main function to cross-check all customers
export const crossCheckAllCustomers = (
  detailsData: any,
  existingCustomers: ExistingCustomer[]
): Map<string, CustomerMatch[]> => {
  const allMatches = new Map<string, CustomerMatch[]>();

  // Check individuals
  if (detailsData.individuals) {
    detailsData.individuals.forEach((individual: any, index: number) => {
      const matches = checkIndividualCustomerMatch(individual, existingCustomers);
      if (matches.length > 0) {
        allMatches.set(`individual_${index}`, matches);
      }
    });
  }

  // Check partners
  if (detailsData.partners) {
    detailsData.partners.forEach((partner: any, index: number) => {
      const matches = checkPartnerCustomerMatch(partner, existingCustomers);
      if (matches.length > 0) {
        allMatches.set(`partner_${index}`, matches);
      }
    });
  }

  // Check group members
  if (detailsData.groups) {
    detailsData.groups.forEach((group: any, groupIndex: number) => {
      if (group.members) {
        group.members.forEach((member: any, memberIndex: number) => {
          const matches = checkGroupMemberMatch(member, existingCustomers);
          if (matches.length > 0) {
            allMatches.set(`group_${groupIndex}_member_${memberIndex}`, matches);
          }
        });
      }
    });
  }

  // Check company directors
  if (detailsData.companies) {
    detailsData.companies.forEach((company: any, companyIndex: number) => {
      if (company.directors) {
        company.directors.forEach((director: any, directorIndex: number) => {
          const matches = checkDirectorMatch(director, existingCustomers);
          if (matches.length > 0) {
            allMatches.set(`company_${companyIndex}_director_${directorIndex}`, matches);
          }
        });
      }
    });
  }

  return allMatches;
};
